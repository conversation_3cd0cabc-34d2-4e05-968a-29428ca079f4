import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { config } from './config';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';
import { authMiddleware } from './middleware/auth';
import { proxyMiddleware } from './middleware/proxy';
import { healthRouter } from './routes/health';
import { authRouter } from './routes/auth';

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS configuration
app.use(cors({
  origin: config.corsOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID']
}));

// Compression
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimitWindowMs,
  max: config.rateLimitMaxRequests,
  message: {
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP, please try again later.',
      timestamp: new Date().toISOString()
    }
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use(limiter);

// Body parsing - exclude proxy routes to avoid stream consumption
app.use((req, res, next) => {
  // Skip body parsing for proxy routes to avoid consuming the request stream
  if (req.path.startsWith('/api/member') ||
      req.path.startsWith('/api/employer') ||
      req.path.startsWith('/api/wellness') ||
      req.path.startsWith('/api/lms') ||
      req.path.startsWith('/api/command-center')) {
    return next();
  }

  // Apply body parsing for non-proxy routes
  express.json({ limit: '10mb' })(req, res, next);
});

app.use((req, res, next) => {
  // Skip URL encoding for proxy routes
  if (req.path.startsWith('/api/member') ||
      req.path.startsWith('/api/employer') ||
      req.path.startsWith('/api/wellness') ||
      req.path.startsWith('/api/lms') ||
      req.path.startsWith('/api/command-center')) {
    return next();
  }

  // Apply URL encoding for non-proxy routes
  express.urlencoded({ extended: true, limit: '10mb' })(req, res, next);
});

// Request logging
app.use(requestLogger);

// Health check routes (no auth required)
app.use('/health', healthRouter);

// Authentication routes (no auth required)
app.use('/api/auth', authRouter);

// Client logging endpoint (no auth required)
app.post('/api/logs/client', express.json({ limit: '1mb' }), (req, res) => {
  try {
    const requestId = req.requestId || 'unknown';

    logger.info('Client logging request received', {
      requestId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      bodySize: JSON.stringify(req.body).length,
    });

    const { logs } = req.body;

    if (!logs) {
      logger.warn('Client logging request missing logs field', { requestId });
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_LOGS',
          message: 'Logs field is required',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
    }

    if (!Array.isArray(logs)) {
      logger.warn('Client logging request logs field is not an array', {
        requestId,
        logsType: typeof logs
      });
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Logs must be an array',
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
    }

    // Log each client log entry with enhanced metadata
    let processedCount = 0;
    logs.forEach((logEntry: any, index: number) => {
      try {
        const { level, message, timestamp, url, userAgent, sessionId, metadata } = logEntry;

        // Map client log levels to logger methods
        switch (level) {
          case 'error':
            logger.error('Client log', {
              clientMessage: message,
              clientTimestamp: timestamp,
              clientUrl: url,
              clientUserAgent: userAgent,
              clientSessionId: sessionId,
              clientMetadata: metadata,
              requestId,
              logIndex: index,
            });
            break;
          case 'warn':
            logger.warn('Client log', {
              clientMessage: message,
              clientTimestamp: timestamp,
              clientUrl: url,
              clientUserAgent: userAgent,
              clientSessionId: sessionId,
              clientMetadata: metadata,
              requestId,
              logIndex: index,
            });
            break;
          case 'debug':
            logger.debug('Client log', {
              clientMessage: message,
              clientTimestamp: timestamp,
              clientUrl: url,
              clientUserAgent: userAgent,
              clientSessionId: sessionId,
              clientMetadata: metadata,
              requestId,
              logIndex: index,
            });
            break;
          default:
            logger.info('Client log', {
              clientLevel: level,
              clientMessage: message,
              clientTimestamp: timestamp,
              clientUrl: url,
              clientUserAgent: userAgent,
              clientSessionId: sessionId,
              clientMetadata: metadata,
              requestId,
              logIndex: index,
            });
        }
        processedCount++;
      } catch (logError) {
        logger.error('Error processing individual client log entry', {
          requestId,
          logIndex: index,
          error: logError instanceof Error ? logError.message : 'Unknown error',
          logEntry: JSON.stringify(logEntry).substring(0, 200),
        });
      }
    });

    logger.info('Client logs processed successfully', {
      requestId,
      totalLogs: logs.length,
      processedLogs: processedCount,
    });

    return res.json({
      success: true,
      data: {
        message: 'Logs received and processed',
        totalCount: logs.length,
        processedCount,
        requestId,
      },
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    logger.error('Client logging endpoint error', {
      requestId: req.requestId,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to process client logs',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      },
    });
  }
});

// OTP routes (no auth required - part of login flow)
app.use('/api/command-center/auth', express.json({ limit: '10mb' }), async (req, res) => {
  try {
    // Construct the correct path: /auth + req.path
    const targetPath = `/auth${req.path}`;
    const targetUrl = `http://localhost:3005${targetPath}`;

    const fetchOptions: any = {
      method: req.method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (req.method !== 'GET') {
      fetchOptions.body = JSON.stringify(req.body);
    }

    const response = await fetch(targetUrl, fetchOptions);
    const data = await response.text();

    res.status(response.status);
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });
    res.send(data);
  } catch (error) {
    logger.error('OTP proxy error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      path: req.path,
      method: req.method
    });
    res.status(500).json({
      error: 'Proxy error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Employee invitation validation routes (no auth required - part of registration flow)
app.use('/api/employer/user-management/validate-invitation-token', express.json({ limit: '10mb' }), async (req, res) => {
  try {
    const targetUrl = `http://localhost:3002/user-management/validate-invitation-token${req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : ''}`;

    const fetchOptions: any = {
      method: req.method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (req.method !== 'GET') {
      fetchOptions.body = JSON.stringify(req.body);
    }

    const response = await fetch(targetUrl, fetchOptions);
    const data = await response.text();

    res.status(response.status);
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });
    res.send(data);
  } catch (error) {
    logger.error('Employee invitation validation proxy error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      path: req.path,
      method: req.method
    });
    res.status(500).json({
      error: 'Proxy error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.use('/api/employer/user-management/confirm-account', express.json({ limit: '10mb' }), async (req, res) => {
  try {
    const targetUrl = `http://localhost:3002/user-management/confirm-account${req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : ''}`;

    const fetchOptions: any = {
      method: req.method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (req.method !== 'GET') {
      fetchOptions.body = JSON.stringify(req.body);
    }

    const response = await fetch(targetUrl, fetchOptions);
    const data = await response.text();

    res.status(response.status);
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });
    res.send(data);
  } catch (error) {
    logger.error('Employee account confirmation proxy error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      path: req.path,
      method: req.method
    });
    res.status(500).json({
      error: 'Proxy error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Protected routes with authentication
app.use('/api', authMiddleware);

// Service proxy routes
app.use('/api/member', proxyMiddleware('member-service', { '^/api/member': '' }));
app.use('/api/employer', proxyMiddleware('employer-service', { '^/api/employer': '' }));
app.use('/api/wellness', proxyMiddleware('wellness-central', { '^/api/wellness': '' }));
app.use('/api/lms', proxyMiddleware('zenx-lms', { '^/api/lms': '' }));
app.use('/api/command-center', proxyMiddleware('command-center', { '^/api/command-center': '' }));

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      code: 'NOT_FOUND',
      message: 'Endpoint not found',
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
      method: req.method
    }
  });
});

// Error handling
app.use(errorHandler);

// Start server
const server = app.listen(config.port, () => {
  logger.info(`API Gateway started on port ${config.port}`, {
    environment: config.nodeEnv,
    corsOrigins: config.corsOrigins,
    services: config.services
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;
