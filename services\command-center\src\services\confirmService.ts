
import { decryptData } from '../../../utils/cryptoUtils';
import { confirmAccount } from './CognitoService';
import { logger } from '../../../utils/logger';
import {
  CognitoIdentityProviderClient,
  ListUsersCommand,
} from '@aws-sdk/client-cognito-identity-provider';

// Initialize Cognito client
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1', // e.g., 'us-east-1'
});

export interface ConfirmationResult {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    userId: string;
    email?: string;
    username?: string;
  };
}

export async function confirmEmailService(token: string): Promise<ConfirmationResult> {
  try {
    // First, decrypt and validate the token
    let username: string;
    try {
      username = decryptData(token);
      if (!username) {
        return {
          success: false,
          message: 'Invalid confirmation token',
          error: 'INVALID_TOKEN'
        };
      }
    } catch (decryptError: any) {
      if (decryptError.code === 'LINK_EXPIRED') {
        return {
          success: false,
          message: 'Confirmation link has expired. Please request a new confirmation email.',
          error: 'LINK_EXPIRED'
        };
      }

      logger.error('Token decryption failed', {
        error: decryptError.message,
        code: decryptError.code
      });

      return {
        success: false,
        message: 'Invalid confirmation link',
        error: 'INVALID_TOKEN'
      };
    }

    // Use the existing Cognito confirmation service
    logger.info('Attempting Cognito account confirmation', {
      username: username,
      tokenLength: token.length
    });
console.log(username,"username");
    const SubToUsername = await getUsernameBySub(username);
    console.log(SubToUsername,"SubToUsername");
    const cognitoResult = await confirmAccount(SubToUsername!);

    logger.info('Cognito confirmation result', {
      status: cognitoResult.status,
      message: cognitoResult.message,
      error: cognitoResult.error,
      hasData: !!cognitoResult.data
    });

    if (cognitoResult.status === 1) {
      // Success
      return {
        success: true,
        message: 'Account confirmed successfully',
        data: {
          userId: username,
          username: username
        }
      };
    } else if (cognitoResult.status === 3) {
      // Link expired
      return {
        success: false,
        message: cognitoResult.message,
        error: 'LINK_EXPIRED'
      };
    } else {
      // Other errors
      logger.error('Cognito confirmation failed', {
        status: cognitoResult.status,
        message: cognitoResult.message,
        error: cognitoResult.error
      });

      return {
        success: false,
        message: cognitoResult.message ?? 'Account confirmation failed',
        error: cognitoResult.error ?? 'CONFIRMATION_FAILED'
      };
    }

  } catch (err) {
    logger.error('Error in confirmation service:', {
      error: err instanceof Error ? err.message : 'Unknown error',
      stack: err instanceof Error ? err.stack : undefined
    });

    return {
      success: false,
      message: 'Internal error during account confirmation',
      error: 'INTERNAL_ERROR'
    };
  }
}

//TO Convert sub to username
async function getUsernameBySub(sub: string): Promise<string | null> {
  const command = new ListUsersCommand({
    UserPoolId: process.env.USERPOOLID!,
    Filter: `sub = "${sub}"`,
    Limit: 1,
  });

  const response = await cognitoClient.send(command);
console.log(response,"response");
  const user = response.Users?.[0];
  return user?.Username || null; // This will be phone/email (Username in Cognito)
}

